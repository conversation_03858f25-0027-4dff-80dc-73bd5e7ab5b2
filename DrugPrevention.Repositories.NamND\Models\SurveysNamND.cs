﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DrugPrevention.Repositories.NamND.Models;

public partial class SurveysNamND
{
    public int SurveyNamNDID { get; set; }

    public string SurveyName { get; set; }

    public string SurveyType { get; set; }

    public string Description { get; set; }

    public string Instructions { get; set; }

    public int TotalQuestions { get; set; }

    public int? MaxRiskScore { get; set; }

    public string TargetAudience { get; set; }

    public bool IsActive { get; set; }

    public bool? IsAnonymous { get; set; }

    public int? EstimatedTimeMinutes { get; set; }

    public int? MinAge { get; set; }

    public int? MaxAge { get; set; }

    public string Language { get; set; }

    public string Category { get; set; }

    public string SurveyPurpose { get; set; }

    public string SurveyStatus { get; set; }

    public int? DisplayOrder { get; set; }

    public string IconURL { get; set; }

    public int Creator { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime? LastUpdated { get; set; }

    public virtual UsersTuyenTM CreatorNavigation { get; set; }

    public virtual ICollection<SurveyQuestionsNamND> SurveyQuestionsNamNDs { get; set; } = new List<SurveyQuestionsNamND>();

    public virtual ICollection<UserSurveysNamND> UserSurveysNamNDs { get; set; } = new List<UserSurveysNamND>();
}
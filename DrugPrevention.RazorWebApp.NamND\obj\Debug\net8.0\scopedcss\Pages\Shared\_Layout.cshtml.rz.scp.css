/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-0m0fq107tp] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-0m0fq107tp] {
  color: #0077cc;
}

.btn-primary[b-0m0fq107tp] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-0m0fq107tp], .nav-pills .show > .nav-link[b-0m0fq107tp] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-0m0fq107tp] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-0m0fq107tp] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-0m0fq107tp] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-0m0fq107tp] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-0m0fq107tp] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}

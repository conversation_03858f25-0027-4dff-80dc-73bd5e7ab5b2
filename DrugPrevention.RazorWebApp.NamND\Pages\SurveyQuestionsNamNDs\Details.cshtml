@page
@model DrugPrevention.RazorWebApp.NamND.Pages.SurveyQuestionsNamNDs.DetailsModel

@{
    ViewData["Title"] = "Survey Question Details";
}

<div class="container-fluid mt-4">
    <div class="card shadow-sm mb-4">
        <div class="card-header bg-gradient-primary text-white py-3">
            <div class="d-flex justify-content-center align-items-center">
                <h1 class="h3 mb-0 font-weight-bold text-white">Survey Question Details</h1>
            </div>
        </div>
        <div class="card-body p-4">
            <div class="details-list" id="surveyQuestionsNamNDId">
                <!-- Group 1: Identification & Context -->
                <div class="row">
                    <dt class="col-sm-3">
                        @Html.DisplayNameFor(model => model.SurveyQuestionsNamND.QuestionText)
                    </dt>
                    <dd class="col-sm-9" id="SurveyQuestionsNamND_QuestionText">
                        @Html.DisplayFor(model => model.SurveyQuestionsNamND.QuestionText)
                    </dd>
                </div>

                <div class="row">
                    <dt class="col-sm-3">
                        @Html.DisplayNameFor(model => model.SurveyQuestionsNamND.SurveyNamND)
                    </dt>
                    <dd class="col-sm-9" id="SurveyQuestionsNamND_SurveyNamND_SurveyName">
                        @Html.DisplayFor(model => model.SurveyQuestionsNamND.SurveyNamND.SurveyName)
                    </dd>
                </div>

                <div class="row">
                    <dt class="col-sm-3">
                        @Html.DisplayNameFor(model => model.SurveyQuestionsNamND.QuestionOrder)
                    </dt>
                    <dd class="col-sm-9" id="SurveyQuestionsNamND_QuestionOrder">
                        @Html.DisplayFor(model => model.SurveyQuestionsNamND.QuestionOrder)
                    </dd>
                </div>

                <!-- Group 2: Basic Configuration -->
                <div class="row">
                    <dt class="col-sm-3">
                        @Html.DisplayNameFor(model => model.SurveyQuestionsNamND.QuestionType)
                    </dt>
                    <dd class="col-sm-9" id="SurveyQuestionsNamND_QuestionType">
                        @Html.DisplayFor(model => model.SurveyQuestionsNamND.QuestionType)
                    </dd>
                </div>

                <div class="row">
                    <dt class="col-sm-3">
                        @Html.DisplayNameFor(model => model.SurveyQuestionsNamND.IsRequired)
                    </dt>
                    <dd class="col-sm-9" id="SurveyQuestionsNamND_IsRequired">
                        <span class="required-badge @(Model.SurveyQuestionsNamND.IsRequired ? "required" : "optional")">
                            @(Model.SurveyQuestionsNamND.IsRequired ? "Required" : "Optional")
                        </span>
                    </dd>
                </div>

                <div class="row">
                    <dt class="col-sm-3">
                        @Html.DisplayNameFor(model => model.SurveyQuestionsNamND.IsActive)
                    </dt>
                    <dd class="col-sm-9" id="SurveyQuestionsNamND_IsActive">
                        <span class="status-badge @(Model.SurveyQuestionsNamND.IsActive ? "active" : "inactive")">
                            @(Model.SurveyQuestionsNamND.IsActive ? "Active" : "Inactive")
                        </span>
                    </dd>
                </div>

                <!-- Group 3: Detailed Content -->
                <div class="row">
                    <dt class="col-sm-3">
                        @Html.DisplayNameFor(model => model.SurveyQuestionsNamND.Options)
                    </dt>
                    <dd class="col-sm-9" id="SurveyQuestionsNamND_Options">
                        @Html.DisplayFor(model => model.SurveyQuestionsNamND.Options)
                    </dd>
                </div>

                <div class="row">
                    <dt class="col-sm-3">
                        @Html.DisplayNameFor(model => model.SurveyQuestionsNamND.HelpText)
                    </dt>
                    <dd class="col-sm-9" id="SurveyQuestionsNamND_HelpText">
                        @Html.DisplayFor(model => model.SurveyQuestionsNamND.HelpText)
                    </dd>
                </div>

                <div class="row">
                    <dt class="col-sm-3">
                        @Html.DisplayNameFor(model => model.SurveyQuestionsNamND.RiskWeight)
                    </dt>
                    <dd class="col-sm-9" id="SurveyQuestionsNamND_RiskWeight">
                        @Html.DisplayFor(model => model.SurveyQuestionsNamND.RiskWeight)
                    </dd>
                </div>

                <!-- Group 4: Metadata -->
                <div class="row">
                    <dt class="col-sm-3">
                        @Html.DisplayNameFor(model => model.SurveyQuestionsNamND.CreatedDate)
                    </dt>
                    <dd class="col-sm-9" id="SurveyQuestionsNamND_CreatedDate">
                        @Html.DisplayFor(model => model.SurveyQuestionsNamND.CreatedDate)
                    </dd>
                </div>

                <!-- Group 5: Additional Configuration -->
                <div class="row">
                    <dt class="col-sm-3">
                        @Html.DisplayNameFor(model => model.SurveyQuestionsNamND.Section)
                    </dt>
                    <dd class="col-sm-9" id="SurveyQuestionsNamND_Section">
                        @Html.DisplayFor(model => model.SurveyQuestionsNamND.Section)
                    </dd>
                </div>

                <div class="row">
                    <dt class="col-sm-3">
                        @Html.DisplayNameFor(model => model.SurveyQuestionsNamND.Tag)
                    </dt>
                    <dd class="col-sm-9" id="SurveyQuestionsNamND_Tag">
                        @Html.DisplayFor(model => model.SurveyQuestionsNamND.Tag)
                    </dd>
                </div>

                <div class="row">
                    <dt class="col-sm-3">
                        @Html.DisplayNameFor(model => model.SurveyQuestionsNamND.Language)
                    </dt>
                    <dd class="col-sm-9" id="SurveyQuestionsNamND_Language">
                        @Html.DisplayFor(model => model.SurveyQuestionsNamND.Language)
                    </dd>
                </div>

                <!-- Group 6: Question Dependencies -->
                <div class="row">
                    <dt class="col-sm-3">
                        @Html.DisplayNameFor(model => model.SurveyQuestionsNamND.DependsOnQuestionID)
                    </dt>
                    <dd class="col-sm-9" id="SurveyQuestionsNamND_DependsOnQuestionID">
                        @Html.DisplayFor(model => model.SurveyQuestionsNamND.DependsOnQuestionID)
                    </dd>
                </div>

                <div class="row">
                    <dt class="col-sm-3">
                        @Html.DisplayNameFor(model => model.SurveyQuestionsNamND.DependsOnAnswer)
                    </dt>
                    <dd class="col-sm-9" id="SurveyQuestionsNamND_DependsOnAnswer">
                        @Html.DisplayFor(model => model.SurveyQuestionsNamND.DependsOnAnswer)
                    </dd>
                </div>

                <!-- Group 7: Scoring Configuration -->
                <div class="row">
                    <dt class="col-sm-3">
                        @Html.DisplayNameFor(model => model.SurveyQuestionsNamND.MinScore)
                    </dt>
                    <dd class="col-sm-9" id="SurveyQuestionsNamND_MinScore">
                        @Html.DisplayFor(model => model.SurveyQuestionsNamND.MinScore)
                    </dd>
                </div>

                <div class="row">
                    <dt class="col-sm-3">
                        @Html.DisplayNameFor(model => model.SurveyQuestionsNamND.MaxScore)
                    </dt>
                    <dd class="col-sm-9" id="SurveyQuestionsNamND_MaxScore">
                        @Html.DisplayFor(model => model.SurveyQuestionsNamND.MaxScore)
                    </dd>
                </div>

                <!-- Group 8: Display Options -->
                <div class="row">
                    <dt class="col-sm-3">
                        @Html.DisplayNameFor(model => model.SurveyQuestionsNamND.DisplayStyle)
                    </dt>
                    <dd class="col-sm-9" id="SurveyQuestionsNamND_DisplayStyle">
                        @Html.DisplayFor(model => model.SurveyQuestionsNamND.DisplayStyle)
                    </dd>
                </div>

                <div class="row">
                    <dt class="col-sm-3">
                        @Html.DisplayNameFor(model => model.SurveyQuestionsNamND.ImageURL)
                    </dt>
                    <dd class="col-sm-9" id="SurveyQuestionsNamND_ImageURL">
                        @Html.DisplayFor(model => model.SurveyQuestionsNamND.ImageURL)
                    </dd>
                </div>
            </div>
        </div>
        <div class="card-footer bg-light py-3">
            <div class="action-buttons-footer" id="SurveyQuestionsNamND_QuestionNamNDID">
                <a asp-page="./Edit" asp-route-id="@Model.SurveyQuestionsNamND.QuestionNamNDID" class="btn btn-primary">
                    <i class="fas fa-edit me-1"></i>Edit Question
                </a>
                <a asp-page="./Index" class="btn btn-secondary">
                    <i class="fas fa-list me-1"></i>Back to List
                </a>
            </div>
        </div>
    </div>
</div>

<style>
    /* Container styling */
    .container-fluid {
        max-width: 1200px;
        margin: 0 auto;
    }

    /* Card styling */
    .card {
        border: none;
        border-radius: 0.5rem;
        overflow: hidden;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
    }

    .card-header {
        padding: 1rem 1.35rem;
        border-bottom: none;
    }

    .card-body {
        padding: 2rem 1.5rem;
    }

    .card-footer {
        border-top: 1px solid #e3e6f0;
        padding: 1rem 1.35rem;
    }

    /* Gradient backgrounds */
    .bg-gradient-primary {
        background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    }

    /* Details list styling */
    .details-list {
        margin-bottom: 0;
    }

    .details-list .row {
        margin-bottom: 1rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #f0f0f0;
    }

    .details-list .row:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
    }

    .details-list dt {
        font-weight: 600;
        color: #2c3e50;
        font-size: 0.9rem;
    }

    .details-list dd {
        font-size: 1rem;
        color: #4a5568;
    }

    /* Button styling */
    .btn {
        border-radius: 0.35rem;
        padding: 0.6rem 1.5rem;
        font-size: 0.95rem;
        font-weight: 600;
        transition: all 0.3s;
        margin-right: 0.5rem;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        letter-spacing: 0.3px;
    }

    .btn i {
        margin-right: 0.5rem;
        font-size: 1rem;
    }

    .btn-primary {
        background-color: #3498db;
        border-color: #3498db;
        color: white;
    }

    .btn-primary:hover {
        background-color: #2980b9;
        border-color: #2980b9;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(52, 152, 219, 0.3);
    }

    .btn-secondary {
        background-color: #95a5a6;
        border-color: #95a5a6;
        color: white;
    }

    .btn-secondary:hover {
        background-color: #7f8c8d;
        border-color: #7f8c8d;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(149, 165, 166, 0.3);
    }

    /* Status badges */
    .status-badge {
        display: inline-block;
        padding: 0.35em 0.65em;
        font-size: 0.75em;
        font-weight: 700;
        line-height: 1;
        text-align: center;
        white-space: nowrap;
        vertical-align: baseline;
        border-radius: 0.25rem;
    }

    .active {
        background-color: #2ecc71;
        color: white;
    }

    .inactive {
        background-color: #e74c3c;
        color: white;
    }

    /* Action buttons footer */
    .action-buttons-footer {
        display: flex;
        gap: 1rem;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
    }

</style>

<script src="~/js/signalr/dist/browser/signalr.js"></script>
<script>
    "use strict";

    var connection = new signalR.HubConnectionBuilder().withUrl("/DrugPreventionHub").build();

    connection.start().then(function () {
        console.log("Connected to the SignalR Hub");
    }).catch(function (err) {
        return console.error(err.toString());
    });

    connection.on("Receiver_DeleteSurveyQuestionsNamND", function (QuestionNamNDID) {
        alert($(`#${QuestionNamNDID}`).value);
        // $('#SurveyQuestionTBodyId').find(`tr[id='${QuestionNamNDID}']`).remove();
        document.getElementById("surveyQuestionsNamNDId").innerHTML = "This item had deleted";
    });

    connection.on("Receiver_UpdateSurveyQuestionsNamND", function (item) {
        //alert(item.code)
        console.log(item);
        // Update the UI with the received item data
        // For example, you can update the text of an element with the new data
        document.getElementById("SurveyQuestionsNamND_QuestionText").innerHTML = item.questionText;
        document.getElementById("SurveyQuestionsNamND_SurveyNamND_SurveyName").innerHTML = item.surveyName;
        document.getElementById("SurveyQuestionsNamND_QuestionOrder").innerHTML = item.questionOrder;
        document.getElementById("SurveyQuestionsNamND_IsActive").innerHTML =
            `<span class="status-badge ${item.isActive ? 'active' : 'inactive'}">
                ${item.isActive ? 'Active' : 'Inactive'}
            </span>`;
        document.getElementById("SurveyQuestionsNamND_CreatedDate").innerHTML = item.createdDate;
        document.getElementById("SurveyQuestionsNamND_QuestionType").innerHTML = item.questionType;
        document.getElementById("SurveyQuestionsNamND_IsRequired").innerHTML =
            `<span class="required-badge ${item.isRequired ? 'required' : 'optional'}">
                ${item.isRequired ? 'Required' : 'Optional'}
            </span>`;
        document.getElementById("SurveyQuestionsNamND_Options").innerHTML = item.options;
        document.getElementById("SurveyQuestionsNamND_RiskWeight").innerHTML = item.riskWeight;
        document.getElementById("SurveyQuestionsNamND_HelpText").innerHTML = item.helpText;
        document.getElementById("SurveyQuestionsNamND_Section").innerHTML = item.section;
        document.getElementById("SurveyQuestionsNamND_Tag").innerHTML = item.tag;
        document.getElementById("SurveyQuestionsNamND_Language").innerHTML = item.language;
        document.getElementById("SurveyQuestionsNamND_DependsOnQuestionID").innerHTML = item.dependsOnQuestionID;
        document.getElementById("SurveyQuestionsNamND_DependsOnAnswer").innerHTML = item.dependsOnAnswer;
        document.getElementById("SurveyQuestionsNamND_MinScore").innerHTML = item.minScore;
        document.getElementById("SurveyQuestionsNamND_MaxScore").innerHTML = item.maxScore;
        document.getElementById("SurveyQuestionsNamND_DisplayStyle").innerHTML = item.displayStyle;
        document.getElementById("SurveyQuestionsNamND_ImageURL").innerHTML = item.imageURL;
    });

</script>

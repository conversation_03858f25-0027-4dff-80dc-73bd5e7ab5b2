﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;

namespace DrugPrevention.Repositories.NamND.Models;

public partial class SU25_PRN222_SE1709_G2_DrugPreventionSystemContext : DbContext
{
    public SU25_PRN222_SE1709_G2_DrugPreventionSystemContext()
    {
    }

    public SU25_PRN222_SE1709_G2_DrugPreventionSystemContext(DbContextOptions<SU25_PRN222_SE1709_G2_DrugPreventionSystemContext> options)
        : base(options)
    {
    }

    public virtual DbSet<AppointmentsNganVHH> AppointmentsNganVHHs { get; set; }

    public virtual DbSet<CommunityProgramsToanN> CommunityProgramsToanNs { get; set; }

    public virtual DbSet<ConsultantScheduleTrongLH> ConsultantScheduleTrongLHs { get; set; }

    public virtual DbSet<ConsultantsTrongLH> ConsultantsTrongLHs { get; set; }

    public virtual DbSet<CoursesQuangTNV> CoursesQuangTNVs { get; set; }

    public virtual DbSet<ProgramParticipantsToanN> ProgramParticipantsToanNs { get; set; }

    public virtual DbSet<SurveyQuestionsNamND> SurveyQuestionsNamNDs { get; set; }

    public virtual DbSet<SurveysNamND> SurveysNamNDs { get; set; }

    public virtual DbSet<System_UserAccount> System_UserAccounts { get; set; }

    public virtual DbSet<UserAppointmentsNganVHH> UserAppointmentsNganVHHs { get; set; }

    public virtual DbSet<UserCoursesTuyenTM> UserCoursesTuyenTMs { get; set; }

    public virtual DbSet<UserSurveysNamND> UserSurveysNamNDs { get; set; }

    public virtual DbSet<UsersTuyenTM> UsersTuyenTMs { get; set; }


    public static string GetConnectionString(string connectionStringName)
    {
        var config = new ConfigurationBuilder()
            .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
            .AddJsonFile("appsettings.json")
            .Build();

        string connectionString = config.GetConnectionString(connectionStringName);
        return connectionString;
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        => optionsBuilder.UseSqlServer(GetConnectionString("DefaultConnection")).UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);

//    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
//#warning To protect potentially sensitive information in your connection string, you should move it out of source code. You can avoid scaffolding the connection string by using the Name= syntax to read it from configuration - see https://go.microsoft.com/fwlink/?linkid=2131148. For more guidance on storing connection strings, see https://go.microsoft.com/fwlink/?LinkId=723263.
//        => optionsBuilder.UseSqlServer("Data Source=BAKKACHICHI\\ANDREW;Initial Catalog=SU25_PRN222_SE1709_G2_DrugPreventionSystem;Persist Security Info=True;User ID=sa;Password=*****;Encrypt=False");

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<AppointmentsNganVHH>(entity =>
        {
            entity.HasKey(e => e.AppointmentNganVHHID).HasName("PK__Appointm__826BF473E7B64DC4");

            entity.ToTable("AppointmentsNganVHH");

            entity.HasIndex(e => e.AppointmentDateTime, "IDX_AppointmentsNganVHH_DateTime");

            entity.HasIndex(e => e.Status, "IDX_AppointmentsNganVHH_Status");

            entity.Property(e => e.AppointmentDateTime).HasColumnType("datetime");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.FeedbackComments).HasColumnType("ntext");
            entity.Property(e => e.IsCancelled).HasDefaultValue(false);
            entity.Property(e => e.MeetingLink).HasMaxLength(255);
            entity.Property(e => e.Status)
                .IsRequired()
                .HasMaxLength(20);

            entity.HasOne(d => d.Consultant).WithMany(p => p.AppointmentsNganVHHs)
                .HasForeignKey(d => d.ConsultantID)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__Appointme__Consu__214BF109");
        });

        modelBuilder.Entity<CommunityProgramsToanN>(entity =>
        {
            entity.HasKey(e => e.ProgramToanNSID).HasName("PK__Communit__CB59144E8E432AA9");

            entity.ToTable("CommunityProgramsToanNS");

            entity.HasIndex(e => e.Status, "IDX_CommunityProgramsToanNS_Status");

            entity.Property(e => e.CurrentParticipants).HasDefaultValue(0);
            entity.Property(e => e.EndDate).HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.Location).HasMaxLength(255);
            entity.Property(e => e.ProgramName)
                .IsRequired()
                .HasMaxLength(100);
            entity.Property(e => e.StartDate).HasColumnType("datetime");
            entity.Property(e => e.Status)
                .IsRequired()
                .HasMaxLength(20);

            entity.HasOne(d => d.Organizer).WithMany(p => p.CommunityProgramsToanNs)
                .HasForeignKey(d => d.OrganizerId)
                .HasConstraintName("FK__Community__Organ__1A9EF37A");
        });

        modelBuilder.Entity<ConsultantScheduleTrongLH>(entity =>
        {
            entity.HasKey(e => e.ScheduleTrongLHID).HasName("PK__Consulta__33BE882E6B4341B1");

            entity.ToTable("ConsultantScheduleTrongLH");

            entity.HasIndex(e => new { e.ConsultantID, e.DayOfWeek, e.StartTime, e.EndTime, e.EffectiveFrom }, "UC_ConsultantSchedule").IsUnique();

            entity.Property(e => e.BufferMinutesBetweenMeetings).HasDefaultValue(15);
            entity.Property(e => e.EffectiveFrom).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.IsAvailable).HasDefaultValue(true);
            entity.Property(e => e.MaxAppointmentsPerSlot).HasDefaultValue(1);
            entity.Property(e => e.Notes).HasColumnType("ntext");
            entity.Property(e => e.RecurringPattern)
                .HasMaxLength(50)
                .HasDefaultValue("Weekly");

            entity.HasOne(d => d.Consultant).WithMany(p => p.ConsultantScheduleTrongLHs)
                .HasForeignKey(d => d.ConsultantID)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__Consultan__Consu__22401542");
        });

        modelBuilder.Entity<ConsultantsTrongLH>(entity =>
        {
            entity.HasKey(e => e.ConsultantTrongLHID).HasName("PK__Consulta__0D38FD8F7348225A");

            entity.ToTable("ConsultantsTrongLH");

            entity.HasIndex(e => e.IsAvailable, "IDX_ConsultantsTrongLH_IsAvailable");

            entity.HasIndex(e => e.Specialization, "IDX_ConsultantsTrongLH_Specialization");

            entity.Property(e => e.AverageRating)
                .HasDefaultValue(0m)
                .HasColumnType("decimal(3, 2)");
            entity.Property(e => e.Certification).HasMaxLength(255);
            entity.Property(e => e.IsAvailable).HasDefaultValue(true);
            entity.Property(e => e.ProfileVerified).HasDefaultValue(false);
            entity.Property(e => e.Qualification).HasMaxLength(255);
            entity.Property(e => e.Specialization)
                .IsRequired()
                .HasMaxLength(100);
            entity.Property(e => e.TotalConsultations).HasDefaultValue(0);

            entity.HasOne(d => d.User).WithMany(p => p.ConsultantsTrongLHs)
                .HasForeignKey(d => d.UserID)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__Consultan__UserI__19AACF41");
        });

        modelBuilder.Entity<CoursesQuangTNV>(entity =>
        {
            entity.HasKey(e => e.CourseQuangTNVID).HasName("PK__CoursesQ__122D2620E56E4783");

            entity.ToTable("CoursesQuangTNV");

            entity.HasIndex(e => e.AgeGroup, "IDX_CoursesQuangTNV_AgeGroup");

            entity.HasIndex(e => e.Category, "IDX_CoursesQuangTNV_Category");

            entity.HasIndex(e => e.IsActive, "IDX_CoursesQuangTNV_IsActive");

            entity.Property(e => e.AgeGroup)
                .IsRequired()
                .HasMaxLength(50);
            entity.Property(e => e.Category)
                .IsRequired()
                .HasMaxLength(50);
            entity.Property(e => e.ContentURL).HasMaxLength(255);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.InstructorName).HasMaxLength(100);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.Rating)
                .HasDefaultValue(0m)
                .HasColumnType("decimal(3, 2)");
            entity.Property(e => e.Title)
                .IsRequired()
                .HasMaxLength(100);
        });

        modelBuilder.Entity<ProgramParticipantsToanN>(entity =>
        {
            entity.HasKey(e => e.ParticipantToanNSID).HasName("PK__ProgramP__C3891D6CA79FBC58");

            entity.ToTable("ProgramParticipantsToanNS");

            entity.Property(e => e.AttendanceStatus).HasMaxLength(20);
            entity.Property(e => e.CertificateIssued).HasDefaultValue(false);
            entity.Property(e => e.FeedbackComments).HasColumnType("ntext");
            entity.Property(e => e.FeedbackProvided).HasDefaultValue(false);
            entity.Property(e => e.ParticipantRole).HasMaxLength(50);
            entity.Property(e => e.RegistrationDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");

            entity.HasOne(d => d.ProgramToanNS).WithMany(p => p.ProgramParticipantsToanNs)
                .HasForeignKey(d => d.ProgramToanNSID)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__ProgramPa__Progr__2334397B");

            entity.HasOne(d => d.User).WithMany(p => p.ProgramParticipantsToanNs)
                .HasForeignKey(d => d.UserID)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__ProgramPa__UserI__1B9317B3");
        });

        modelBuilder.Entity<SurveyQuestionsNamND>(entity =>
        {
            entity.HasKey(e => e.QuestionNamNDID).HasName("PK__SurveyQu__2DE1971EFB53A784");

            entity.ToTable("SurveyQuestionsNamND");

            entity.HasIndex(e => e.IsActive, "IX_SurveyQuestionsNamND_IsActive");

            entity.HasIndex(e => e.QuestionOrder, "IX_SurveyQuestionsNamND_QuestionOrder");

            entity.HasIndex(e => e.SurveyNamNDID, "IX_SurveyQuestionsNamND_SurveyID");

            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.DependsOnAnswer).HasMaxLength(255);
            entity.Property(e => e.DisplayStyle).HasMaxLength(50);
            entity.Property(e => e.HelpText).HasMaxLength(255);
            entity.Property(e => e.ImageURL).HasMaxLength(255);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsRequired).HasDefaultValue(true);
            entity.Property(e => e.Language)
                .HasMaxLength(20)
                .HasDefaultValue("en");
            entity.Property(e => e.Options).HasMaxLength(255);
            entity.Property(e => e.QuestionText)
                .IsRequired()
                .HasMaxLength(255);
            entity.Property(e => e.QuestionType)
                .IsRequired()
                .HasMaxLength(50);
            entity.Property(e => e.RiskWeight).HasDefaultValue(1);
            entity.Property(e => e.Section).HasMaxLength(50);
            entity.Property(e => e.Tag).HasMaxLength(50);

            entity.HasOne(d => d.SurveyNamND).WithMany(p => p.SurveyQuestionsNamNDs)
                .HasForeignKey(d => d.SurveyNamNDID)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__SurveyQue__Surve__6CD828CA");
        });

        modelBuilder.Entity<SurveysNamND>(entity =>
        {
            entity.HasKey(e => e.SurveyNamNDID).HasName("PK__SurveysN__A48BA58E0A1F0184");

            entity.ToTable("SurveysNamND");

            entity.Property(e => e.Category).HasMaxLength(50);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Description).HasMaxLength(255);
            entity.Property(e => e.IconURL).HasMaxLength(255);
            entity.Property(e => e.Instructions).HasMaxLength(255);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsAnonymous).HasDefaultValue(false);
            entity.Property(e => e.Language)
                .HasMaxLength(20)
                .HasDefaultValue("en");
            entity.Property(e => e.LastUpdated)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.SurveyName)
                .IsRequired()
                .HasMaxLength(100);
            entity.Property(e => e.SurveyPurpose).HasMaxLength(255);
            entity.Property(e => e.SurveyStatus).HasMaxLength(50);
            entity.Property(e => e.SurveyType)
                .IsRequired()
                .HasMaxLength(50);
            entity.Property(e => e.TargetAudience).HasMaxLength(50);

            entity.HasOne(d => d.CreatorNavigation).WithMany(p => p.SurveysNamNDs)
                .HasForeignKey(d => d.Creator)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__SurveysNa__Creat__1C873BEC");
        });

        modelBuilder.Entity<System_UserAccount>(entity =>
        {
            entity.HasKey(e => e.UserAccountID);

            entity.ToTable("System.UserAccount");

            entity.Property(e => e.ApplicationCode).HasMaxLength(50);
            entity.Property(e => e.CreatedBy).HasMaxLength(50);
            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.Email)
                .IsRequired()
                .HasMaxLength(150);
            entity.Property(e => e.EmployeeCode)
                .IsRequired()
                .HasMaxLength(50);
            entity.Property(e => e.FullName)
                .IsRequired()
                .HasMaxLength(100);
            entity.Property(e => e.ModifiedBy).HasMaxLength(50);
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
            entity.Property(e => e.Password)
                .IsRequired()
                .HasMaxLength(100);
            entity.Property(e => e.Phone)
                .IsRequired()
                .HasMaxLength(50);
            entity.Property(e => e.RequestCode).HasMaxLength(50);
            entity.Property(e => e.UserName)
                .IsRequired()
                .HasMaxLength(50);
        });

        modelBuilder.Entity<UserAppointmentsNganVHH>(entity =>
        {
            entity.HasKey(e => e.UserAppointmentNganVHHID).HasName("PK__UserAppo__FB1589F10C163D80");

            entity.ToTable("UserAppointmentsNganVHH");

            entity.Property(e => e.AdditionalNotes).HasColumnType("ntext");
            entity.Property(e => e.BookingDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.FollowUpNotes).HasColumnType("ntext");
            entity.Property(e => e.FollowUpRequired).HasDefaultValue(false);
            entity.Property(e => e.PostAppointmentFeedbackProvided).HasDefaultValue(false);
            entity.Property(e => e.PreAppointmentSurveyCompleted).HasDefaultValue(false);
            entity.Property(e => e.PrimaryReason).HasMaxLength(255);

            entity.HasOne(d => d.Appointment).WithMany(p => p.UserAppointmentsNganVHHs)
                .HasForeignKey(d => d.AppointmentID)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__UserAppoi__Appoi__2057CCD0");

            entity.HasOne(d => d.User).WithMany(p => p.UserAppointmentsNganVHHs)
                .HasForeignKey(d => d.UserID)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__UserAppoi__UserI__1D7B6025");
        });

        modelBuilder.Entity<UserCoursesTuyenTM>(entity =>
        {
            entity.HasKey(e => e.UserCourseTuyenTMID).HasName("PK__UserCour__ACC2941923E4F910");

            entity.ToTable("UserCoursesTuyenTM");

            entity.Property(e => e.CertificateIssued).HasDefaultValue(false);
            entity.Property(e => e.CompletionDate).HasColumnType("datetime");
            entity.Property(e => e.EnrollmentDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.LastAccessDate).HasColumnType("datetime");
            entity.Property(e => e.Progress)
                .HasDefaultValue(0m)
                .HasColumnType("decimal(5, 2)");

            entity.HasOne(d => d.Course).WithMany(p => p.UserCoursesTuyenTMs)
                .HasForeignKey(d => d.CourseID)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__UserCours__Cours__24285DB4");

            entity.HasOne(d => d.User).WithMany(p => p.UserCoursesTuyenTMs)
                .HasForeignKey(d => d.UserID)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__UserCours__UserI__1E6F845E");
        });

        modelBuilder.Entity<UserSurveysNamND>(entity =>
        {
            entity.HasKey(e => e.UserSurveyNamNDID).HasName("PK__UserSurv__7AAF6C20BFAAA2E1");

            entity.ToTable("UserSurveysNamND");

            entity.Property(e => e.DeviceInfo).HasMaxLength(100);
            entity.Property(e => e.EndTime).HasColumnType("datetime");
            entity.Property(e => e.Feedback).HasMaxLength(255);
            entity.Property(e => e.IPAddress).HasMaxLength(50);
            entity.Property(e => e.IsAnonymous).HasDefaultValue(false);
            entity.Property(e => e.IsCompleted).HasDefaultValue(false);
            entity.Property(e => e.Language)
                .HasMaxLength(20)
                .HasDefaultValue("en");
            entity.Property(e => e.Location).HasMaxLength(100);
            entity.Property(e => e.Recommendations).HasMaxLength(255);
            entity.Property(e => e.ResultSummary).HasMaxLength(255);
            entity.Property(e => e.StartTime).HasColumnType("datetime");
            entity.Property(e => e.Status).HasMaxLength(50);
            entity.Property(e => e.SubmissionDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");

            entity.HasOne(d => d.Survey).WithMany(p => p.UserSurveysNamNDs)
                .HasForeignKey(d => d.SurveyID)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__UserSurve__Surve__2610A626");

            entity.HasOne(d => d.User).WithMany(p => p.UserSurveysNamNDs)
                .HasForeignKey(d => d.UserID)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__UserSurve__UserI__1F63A897");
        });

        modelBuilder.Entity<UsersTuyenTM>(entity =>
        {
            entity.HasKey(e => e.UserTuyenTMID).HasName("PK__UsersTuy__1042E1E092B53572");

            entity.ToTable("UsersTuyenTM");

            entity.HasIndex(e => e.IsActive, "IDX_UsersTuyenTM_IsActive");

            entity.HasIndex(e => e.Role, "IDX_UsersTuyenTM_Role");

            entity.HasIndex(e => e.Username, "UQ__UsersTuy__536C85E49E410CCB").IsUnique();

            entity.HasIndex(e => e.Email, "UQ__UsersTuy__A9D1053488ECFAD7").IsUnique();

            entity.Property(e => e.Email)
                .IsRequired()
                .HasMaxLength(100);
            entity.Property(e => e.FirstName).HasMaxLength(50);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.LastName).HasMaxLength(50);
            entity.Property(e => e.Password)
                .IsRequired()
                .HasMaxLength(255);
            entity.Property(e => e.PhoneNumber).HasMaxLength(20);
            entity.Property(e => e.RegistrationDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Role)
                .IsRequired()
                .HasMaxLength(20);
            entity.Property(e => e.Username)
                .IsRequired()
                .HasMaxLength(50);
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
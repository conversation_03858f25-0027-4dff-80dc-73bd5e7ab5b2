﻿@page
@model DrugPrevention.RazorWebApp.NamND.Pages.SurveyQuestionsNamNDs.CreateModel

@{
    ViewData["Title"] = "Create Survey Question";
}

<div class="container-fluid mt-4">
    <div class="card shadow-sm mb-4">
        <div class="card-header bg-gradient-primary text-white py-3">
            <div class="d-flex justify-content-center align-items-center">
                <h1 class="h3 mb-0 font-weight-bold text-white">
                    <i class="fas fa-plus-circle me-2"></i>Create Survey Question
                </h1>
            </div>
        </div>
        <div class="card-body p-4">
            <form method="post">
                <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                
                <!-- 1. Thông tin cơ bản -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Basic Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="SurveyQuestionsNamND.SurveyNamNDID" class="control-label"></label>
                                    <select asp-for="SurveyQuestionsNamND.SurveyNamNDID" class="form-control" asp-items="ViewBag.SurveyNamNDID"></select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="SurveyQuestionsNamND.QuestionOrder" class="control-label"></label>
                                    <input asp-for="SurveyQuestionsNamND.QuestionOrder" class="form-control" />
                                    <span asp-validation-for="SurveyQuestionsNamND.QuestionOrder" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label asp-for="SurveyQuestionsNamND.QuestionText" class="control-label"></label>
                            <textarea asp-for="SurveyQuestionsNamND.QuestionText" class="form-control" rows="3"></textarea>
                            <span asp-validation-for="SurveyQuestionsNamND.QuestionText" class="text-danger"></span>
                        </div>
                        
                        <div class="form-group">
                            <label asp-for="SurveyQuestionsNamND.QuestionType" class="control-label"></label>
                            <input asp-for="SurveyQuestionsNamND.QuestionType" class="form-control" />
                            <span asp-validation-for="SurveyQuestionsNamND.QuestionType" class="text-danger"></span>
                        </div>
                    </div>
                </div>
                
                <!-- 2. Tùy chọn và hiển thị -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Display Options</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label asp-for="SurveyQuestionsNamND.Options" class="control-label"></label>
                                    <textarea asp-for="SurveyQuestionsNamND.Options" class="form-control" rows="3"></textarea>
                                    <span asp-validation-for="SurveyQuestionsNamND.Options" class="text-danger"></span>
                                    <small class="form-text text-muted">Enter options separated by commas or new lines</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="SurveyQuestionsNamND.HelpText" class="control-label"></label>
                                    <input asp-for="SurveyQuestionsNamND.HelpText" class="form-control" />
                                    <span asp-validation-for="SurveyQuestionsNamND.HelpText" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="SurveyQuestionsNamND.DisplayStyle" class="control-label"></label>
                                    <input asp-for="SurveyQuestionsNamND.DisplayStyle" class="form-control" />
                                    <span asp-validation-for="SurveyQuestionsNamND.DisplayStyle" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label asp-for="SurveyQuestionsNamND.ImageURL" class="control-label"></label>
                            <input asp-for="SurveyQuestionsNamND.ImageURL" class="form-control" />
                            <span asp-validation-for="SurveyQuestionsNamND.ImageURL" class="text-danger"></span>
                        </div>
                    </div>
                </div>
                
                <!-- 3. Phân loại và phân nhóm -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Classification</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label asp-for="SurveyQuestionsNamND.Section" class="control-label"></label>
                                    <input asp-for="SurveyQuestionsNamND.Section" class="form-control" />
                                    <span asp-validation-for="SurveyQuestionsNamND.Section" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label asp-for="SurveyQuestionsNamND.Tag" class="control-label"></label>
                                    <input asp-for="SurveyQuestionsNamND.Tag" class="form-control" />
                                    <span asp-validation-for="SurveyQuestionsNamND.Tag" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label asp-for="SurveyQuestionsNamND.Language" class="control-label"></label>
                                    <input asp-for="SurveyQuestionsNamND.Language" class="form-control" />
                                    <span asp-validation-for="SurveyQuestionsNamND.Language" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 4. Điểm số và đánh giá rủi ro -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Scoring & Risk Assessment</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label asp-for="SurveyQuestionsNamND.MinScore" class="control-label"></label>
                                    <input asp-for="SurveyQuestionsNamND.MinScore" class="form-control" />
                                    <span asp-validation-for="SurveyQuestionsNamND.MinScore" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label asp-for="SurveyQuestionsNamND.MaxScore" class="control-label"></label>
                                    <input asp-for="SurveyQuestionsNamND.MaxScore" class="form-control" />
                                    <span asp-validation-for="SurveyQuestionsNamND.MaxScore" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label asp-for="SurveyQuestionsNamND.RiskWeight" class="control-label"></label>
                                    <input asp-for="SurveyQuestionsNamND.RiskWeight" class="form-control" />
                                    <span asp-validation-for="SurveyQuestionsNamND.RiskWeight" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 5. Trạng thái -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Status</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" asp-for="SurveyQuestionsNamND.IsActive" checked />
                                    <label class="form-check-label" asp-for="SurveyQuestionsNamND.IsActive"></label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" asp-for="SurveyQuestionsNamND.IsRequired" />
                                    <label class="form-check-label" asp-for="SurveyQuestionsNamND.IsRequired"></label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 6. Điều kiện phụ thuộc -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Dependencies (Advanced)</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="SurveyQuestionsNamND.DependsOnQuestionID" class="control-label"></label>
                                    <input asp-for="SurveyQuestionsNamND.DependsOnQuestionID" class="form-control" />
                                    <span asp-validation-for="SurveyQuestionsNamND.DependsOnQuestionID" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="SurveyQuestionsNamND.DependsOnAnswer" class="control-label"></label>
                                    <input asp-for="SurveyQuestionsNamND.DependsOnAnswer" class="form-control" />
                                    <span asp-validation-for="SurveyQuestionsNamND.DependsOnAnswer" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 7. Thông tin hệ thống -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">System Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label asp-for="SurveyQuestionsNamND.CreatedDate" class="control-label"></label>
                            <input type="text" class="form-control" value="@DateTime.Now.ToString("MM/dd/yyyy HH:mm:ss")" readonly />
                            <input type="hidden" asp-for="SurveyQuestionsNamND.CreatedDate" value="@DateTime.Now" />
                        </div>
                    </div>
                </div>
                
                <!-- Nút submit -->
                <div class="card-footer bg-light py-3">
                    <div class="action-buttons-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus-circle me-1"></i>Create
                        </button>
                        <button type="button" id="btnHubCreate" class="btn btn-success">
                            <i class="fas fa-broadcast-tower me-1"></i>Create by SignalRHub
                        </button>
                        <a asp-page="./Index" class="btn btn-secondary">
                            <i class="fas fa-list me-1"></i>Back to List
                        </a>
                    </div>
                    <div class="mt-2 text-center">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            SignalR: Send data → SignalRHub → send data to all Clients → Create
                        </small>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="~/js/signalr/dist/browser/signalr.js"></script>

<script>
    "use strict";

    var connection = new signalR.HubConnectionBuilder().withUrl("/DrugPreventionHub").build();

    connection.start().then(function () {
        console.log("Connected to the SignalR Hub");
    }).catch(function (err) {
        return console.error(err.toString());
    });
    
    document.getElementById("btnHubCreate").addEventListener("click", function (event) {
        var dataObj = JSON.stringify({
            // 1. Thông tin cơ bản
            SurveyNamNDID: document.getElementById("SurveyQuestionsNamND_SurveyNamNDID").value,
            QuestionText: document.getElementById("SurveyQuestionsNamND_QuestionText").value,
            QuestionOrder: document.getElementById("SurveyQuestionsNamND_QuestionOrder").value,
            QuestionType: document.getElementById("SurveyQuestionsNamND_QuestionType").value,
            
            // 2. Tùy chọn và hiển thị
            Options: document.getElementById("SurveyQuestionsNamND_Options").value,
            HelpText: document.getElementById("SurveyQuestionsNamND_HelpText").value,
            DisplayStyle: document.getElementById("SurveyQuestionsNamND_DisplayStyle").value,
            ImageURL: document.getElementById("SurveyQuestionsNamND_ImageURL").value,
            
            // 3. Phân loại và phân nhóm
            Section: document.getElementById("SurveyQuestionsNamND_Section").value,
            Tag: document.getElementById("SurveyQuestionsNamND_Tag").value,
            Language: document.getElementById("SurveyQuestionsNamND_Language").value,
            
            // 4. Điểm số và đánh giá rủi ro
            MinScore: document.getElementById("SurveyQuestionsNamND_MinScore").value,
            MaxScore: document.getElementById("SurveyQuestionsNamND_MaxScore").value,
            RiskWeight: document.getElementById("SurveyQuestionsNamND_RiskWeight").value,
            
            // 5. Trạng thái
            IsActive: document.getElementById("SurveyQuestionsNamND_IsActive").checked,
            IsRequired: document.getElementById("SurveyQuestionsNamND_IsRequired").checked,
            
            // 6. Điều kiện phụ thuộc
            DependsOnQuestionID: document.getElementById("SurveyQuestionsNamND_DependsOnQuestionID").value,
            DependsOnAnswer: document.getElementById("SurveyQuestionsNamND_DependsOnAnswer").value,
            
            // 7. Thông tin hệ thống
            CreatedDate: document.getElementById("SurveyQuestionsNamND_CreatedDate").value
        });
        console.log(dataObj);

        connection.invoke("HubCreate_SurveyQuestion", dataObj).catch(function (err) {
            return console.error(err.toString());
        });
        event.preventDefault();
    });
</script>

<style>
    /* Container styling */
    .container-fluid {
        max-width: 1200px;
        margin: 0 auto;
    }

    /* Card styling */
    .card {
        border: none;
        border-radius: 0.5rem;
        overflow: hidden;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
    }

    .card-header {
        padding: 1rem 1.35rem;
        border-bottom: none;
    }

    .card-body {
        padding: 1.5rem;
    }

    .card-footer {
        border-top: 1px solid #e3e6f0;
        padding: 1rem 1.35rem;
    }

    /* Gradient backgrounds */
    .bg-gradient-primary {
        background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    }

    /* Form styling */
    .form-group {
        margin-bottom: 1rem;
    }

    .form-control {
        border-radius: 0.35rem;
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
        border: 1px solid #d1d3e2;
        transition: all 0.2s;
    }

    .form-control:focus {
        border-color: #a2d5f2;
        box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    }

    .control-label {
        font-weight: 600;
        font-size: 0.85rem;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    /* Button styling */
    .btn {
        border-radius: 0.35rem;
        padding: 0.6rem 1.5rem;
        font-size: 0.95rem;
        font-weight: 600;
        transition: all 0.3s;
        margin-right: 0.5rem;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        letter-spacing: 0.3px;
    }

    .btn i {
        margin-right: 0.5rem;
        font-size: 1rem;
    }

    .btn-primary {
        background-color: #3498db;
        border-color: #3498db;
        color: white;
    }

    .btn-primary:hover {
        background-color: #2980b9;
        border-color: #2980b9;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(52, 152, 219, 0.3);
    }

    .btn-success {
        background-color: #2ecc71;
        border-color: #2ecc71;
        color: white;
    }

    .btn-success:hover {
        background-color: #27ae60;
        border-color: #27ae60;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(46, 204, 113, 0.3);
    }

    .btn-secondary {
        background-color: #95a5a6;
        border-color: #95a5a6;
        color: white;
    }

    .btn-secondary:hover {
        background-color: #7f8c8d;
        border-color: #7f8c8d;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(149, 165, 166, 0.3);
    }

    /* Typography */
    h1, h3, h5 {
        color: #2c3e50;
        font-weight: 600;
        letter-spacing: 0.02em;
    }

    /* Card header styling */
    .bg-light {
        background-color: #f8f9fc !important;
        border-bottom: 1px solid #eaecf4;
    }

    .card-header h5 {
        color: #3498db;
        font-weight: 600;
    }

    /* Action buttons footer */
    .action-buttons-footer {
        display: flex;
        gap: 1rem;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
    }

    /* Small text styling */
    .text-muted {
        font-size: 0.875rem;
        color: #7f8c8d !important;
    }

    /* Form check styling */
    .form-check-input:checked {
        background-color: #3498db;
        border-color: #3498db;
    }

    /* Section cards styling */
    .card .card {
        box-shadow: 0 0.1rem 0.5rem 0 rgba(58, 59, 69, 0.1) !important;
        margin-bottom: 1.5rem;
        transition: all 0.3s;
    }

    .card .card:hover {
        transform: translateY(-3px);
        box-shadow: 0 0.2rem 1rem 0 rgba(58, 59, 69, 0.15) !important;
    }

    /* Textarea styling */
    textarea.form-control {
        min-height: 100px;
        resize: vertical;
    }

    /* Form validation styling */
    .text-danger {
        font-size: 0.8rem;
        margin-top: 0.25rem;
    }

</style>

@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
}

﻿@page
@model DrugPrevention.RazorWebApp.NamND.Pages.SurveyQuestionsNamNDs.IndexModel

@{
    ViewData["Title"] = "Survey Questions";
}

<div class="container-fluid mt-4">
    <div class="card shadow-lg border-0 mb-4">
        <div class="card-header bg-gradient-primary text-white py-3">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3 mb-0 font-weight-bold">Survey Questions</h1>
                <a asp-page="Create" class="btn btn-light btn-icon-split">
                    <span class="icon text-gray-600">
                        <i class="fas fa-plus"></i>
                    </span>
                    <span class="text">Create New</span>
                </a>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="bg-light">
                        <tr>
                            <th class="text-uppercase font-weight-bold question-text-column">Question Text</th>
                            <th class="text-uppercase font-weight-bold survey-name-column">Survey Name</th>
                            <th class="text-uppercase font-weight-bold order-column">Order</th>
                            <th class="text-uppercase font-weight-bold type-column">Type</th>
                            <th class="text-uppercase font-weight-bold required-column">Required</th>
                            <th class="text-uppercase font-weight-bold status-column">Status</th>
                            <th class="text-uppercase font-weight-bold options-column">Options</th>
                            <th class="text-uppercase font-weight-bold risk-weight-column">Risk Weight</th>
                            <th class="text-uppercase font-weight-bold help-text-column">Help Text</th>
                            <th class="text-uppercase font-weight-bold date-column">Created Date</th>
                            <th class="text-uppercase font-weight-bold action-column">Actions</th>
                        </tr>
                    </thead>
                    <tbody id="SurveyQuestionTBodyId">
                        @foreach (var item in Model.SurveyQuestionsNamND)
                        {
                            <tr id="@item.QuestionNamNDID">
                                <td class="content-cell" style="text-align: left;">@Html.DisplayFor(modelItem => item.QuestionText)</td>
                                <td class="content-cell" style="text-align: left;">@Html.DisplayFor(modelItem => item.SurveyNamND.SurveyName)</td>
                                <td>@Html.DisplayFor(modelItem => item.QuestionOrder)</td>
                                <td>@Html.DisplayFor(modelItem => item.QuestionType)</td>
                                <td>
                                    @if (item.IsRequired)
                                    {
                                        <span class="badge badge-required">Required</span>
                                    }
                                    else
                                    {
                                        <span class="badge badge-optional">Optional</span>
                                    }
                                </td>
                                <td>
                                    @if (item.IsActive)
                                    {
                                        <span class="badge badge-active">Active</span>
                                    }
                                    else
                                    {
                                        <span class="badge badge-inactive">Inactive</span>
                                    }
                                </td>
                                <td class="content-cell" style="text-align: left;">@Html.DisplayFor(modelItem => item.Options)</td>
                                <td>@Html.DisplayFor(modelItem => item.RiskWeight)</td>
                                <td class="content-cell" style="text-align: left;">@Html.DisplayFor(modelItem => item.HelpText)</td>
                                <td class="date-column">
                                    <div class="date-display">
                                        <span class="date-part">@item.CreatedDate?.ToString("yyyy-MM-dd")</span>
                                        <span class="time-part">@item.CreatedDate?.ToString("HH:mm")</span>
                                    </div>
                                </td>
                                <td class="action-column">
                                    <div class="action-buttons">
                                        <a asp-page="./Edit" asp-route-id="@item.QuestionNamNDID" class="btn btn-edit">Edit</a>
                                        <a asp-page="./Details" asp-route-id="@item.QuestionNamNDID" class="btn btn-details">Details</a>
                                        <a asp-page="./Delete" asp-route-id="@item.QuestionNamNDID" class="btn btn-delete">Delete</a>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<style>
    /* Modern container styling */
    .container-fluid {
        max-width: 1280px;
        margin: 0 auto;
    }
    
    /* Enhanced card styling */
    .card {
        border-radius: 0.75rem;
        overflow: hidden;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
        transition: all 0.3s ease;
    }
    
    /* Beautiful gradient header like Privacy page */
    .bg-gradient-primary {
        background: linear-gradient(135deg, #3a47d5 0%, #00d2ff 100%);
    }
    
    .card-header {
        border-bottom: none;
    }
    
    .card-header h1 {
        font-size: 1.5rem;
        font-weight: 700;
        margin: 0;
        letter-spacing: 0.5px;
    }
    
    /* Button styling */
    .btn-light {
        color: #3a47d5;
        background-color: white;
        border-color: white;
        font-weight: 600;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        transition: all 0.2s;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }
    
    .btn-light:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    
    .btn-icon-split {
        display: inline-flex;
        align-items: center;
    }
    
    .btn-icon-split .icon {
        background: rgba(0, 0, 0, 0.05);
        padding: 0.375rem 0.75rem;
        border-radius: 0.5rem 0 0 0.5rem;
        margin-right: 0.5rem;
    }
    
    /* Table styling */
    .table {
        margin-bottom: 0;
    }

    .table-responsive {
        overflow-x: auto;
    }
    
    .table thead {
        background-color: #f8f9fc;
    }
    
    .table th {
        font-size: 0.75rem;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        color: #4a5568;
        padding: 1rem 0.75rem;
        border-bottom: 1px solid #e3e6f0;
        vertical-align: middle;
        white-space: nowrap;
    }
    
    .table th div {
        line-height: 1.2;
    }

    .table th div + div {
        margin-top: 0.25rem;
    }
    
    .table td {
        max-width: 200px;
        white-space: normal;
        word-wrap: break-word;
        padding: 1rem 0.75rem;
        vertical-align: middle;
    }

    .table td.content-cell {
        line-height: 1.4;
    }

    .table tbody tr {
        background-color: #f8f9fc;
        transition: background-color 0.2s;
    }

    .table tbody tr:nth-child(odd) {
        background-color: #ffffff;
    }

    .table tbody tr:hover {
        background-color: #edf2f7;
    }
    
    /* Date column styling */
    .date-column {
        min-width: 100px;
        white-space: nowrap;
    }
    
    .date-display {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
    
    .date-part {
        font-weight: 500;
        color: #4a5568;
    }

    .time-part {
        font-size: 0.75rem;
        color: #718096;
    }
    
    /* Badge styling */
    .badge {
        display: inline-block;
        padding: 0.5em 0.75em;
        font-size: 0.75rem;
        font-weight: 600;
        text-align: center;
        white-space: nowrap;
        border-radius: 0.5rem;
        letter-spacing: 0.3px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .badge-required {
        background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
        color: white;
    }

    .badge-optional {
        background: linear-gradient(135deg, #ecc94b 0%, #d69e2e 100%);
        color: #744210;
    }

    .badge-active {
        background: linear-gradient(135deg, #38b2ac 0%, #319795 100%);
        color: white;
    }

    .badge-inactive {
        background: linear-gradient(135deg, #fc8181 0%, #f56565 100%);
        color: white;
    }

    /* Action buttons styling */
    .action-buttons {
        display: flex;
        gap: 0.25rem;
        justify-content: center;
        flex-wrap: wrap;
    }

    .btn {
        padding: 0.35rem 0.5rem;
        font-size: 0.75rem;
        font-weight: 600;
        border-radius: 0.35rem;
        transition: all 0.2s;
        text-decoration: none;
        letter-spacing: 0px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        border: none;
    }

    .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
        color: white;
    }

    .btn-edit {
        background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
        color: white;
    }

    .btn-edit:hover {
        background: linear-gradient(135deg, #3182ce 0%, #2b6cb0 100%);
    }

    .btn-details {
        background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        color: white;
    }

    .btn-details:hover {
        background: linear-gradient(135deg, #38a169 0%, #2f855a 100%);
    }

    .btn-delete {
        background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
        color: white;
    }

    .btn-delete:hover {
        background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
    }
    
    /* Cell content styling for better text wrapping */
    .question-text-cell, .survey-name-cell, .question-type-cell, .options-cell, .help-text-cell {
        max-width: 200px;
        vertical-align: middle;
    }

    .question-text-content, .survey-name-content, .question-type-content, .options-content, .help-text-content {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-word;
        line-height: 1.4;
    }

    /* Specific column widths */
    .question-text-cell {
        width: 18%;
    }

    .survey-name-cell {
        width: 12%;
    }

    .question-type-cell {
        width: 10%;
    }

    .options-cell {
        width: 15%;
    }

    .help-text-cell {
        width: 15%;
    }
    
    /* Cải thiện hiển thị nội dung trong các ô */
    .table td {
        max-width: 200px;
        white-space: normal;
        word-wrap: break-word;
        padding: 1rem 0.75rem;
        vertical-align: middle;
    }

    /* Thêm style cho các ô có nhiều nội dung */
    .table td.content-cell {
        line-height: 1.4;
    }

    /* Đảm bảo các cột có độ rộng phù hợp */
    .table th {
        white-space: nowrap;
        font-size: 0.7rem;
        padding: 0.75rem 0.5rem;
    }

    /* Đảm bảo bảng có thể cuộn ngang khi cần thiết */
    .table-responsive {
        overflow-x: auto;
    }

    /* Đảm bảo cột ngày có đủ không gian */
    .date-column {
        min-width: 100px;
        white-space: nowrap;
    }

    /* Đảm bảo các nút action có đủ không gian */
    .action-buttons {
        white-space: nowrap;
        display: flex;
        gap: 0.5rem;
        justify-content: center;
    }
    
    /* Cải thiện hiển thị nút action */
    .action-buttons {
        display: flex;
        gap: 0.25rem;
        justify-content: center;
        white-space: nowrap;
    }

    .btn {
        padding: 0.25rem 0.4rem;
        font-size: 0.7rem;
        font-weight: 600;
        border-radius: 0.35rem;
        transition: all 0.2s;
        text-decoration: none;
        letter-spacing: 0px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        border: none;
    }

    .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
        color: white;
    }

    /* Đảm bảo cột action có đủ không gian */
    .action-column {
        min-width: 180px;
        white-space: nowrap;
    }

    /* Giảm padding cho các ô */
    .table td {
        padding: 0.75rem 0.5rem;
        vertical-align: middle;
    }

    /* Đảm bảo bảng có thể cuộn ngang khi cần thiết */
    .table-responsive {
        overflow-x: auto;
    }
    
    /* Giảm kích thước chữ cho toàn bộ bảng */
    .table {
        font-size: 0.85rem;
    }

    /* Giảm kích thước chữ cho tiêu đề cột */
    .table th {
        font-size: 0.7rem;
        padding: 0.75rem 0.5rem;
        white-space: nowrap;
    }

    /* Giảm kích thước chữ cho nội dung cột */
    .table td {
        font-size: 0.85rem;
        padding: 0.75rem 0.5rem;
        vertical-align: middle;
    }

    /* Giảm kích thước chữ cho badge */
    .badge {
        font-size: 0.7rem;
        padding: 0.4em 0.65em;
    }

    /* Giảm kích thước chữ cho date-part và time-part */
    .date-part {
        font-size: 0.85rem;
        font-weight: 500;
        color: #4a5568;
    }

    .time-part {
        font-size: 0.7rem;
        color: #718096;
    }

    /* Giảm kích thước chữ cho nội dung trong các ô có nhiều text */
    .content-cell {
        font-size: 0.85rem;
        line-height: 1.3;
    }

    /* Căn giữa tiêu đề cột và nội dung */
    .table th {
        text-align: center;
        vertical-align: middle;
        font-size: 0.7rem;
        padding: 0.75rem 0.5rem;
        white-space: nowrap;
    }

    .table td {
        text-align: center;
        vertical-align: middle;
        font-size: 0.85rem;
        padding: 0.75rem 0.5rem;
    }

    /* Đối với các cột có nhiều nội dung text, căn trái để dễ đọc */
    .content-cell {
        text-align: left;
        font-size: 0.85rem;
        line-height: 1.3;
    }

    /* Đảm bảo tiêu đề cột được căn giữa */
    .table thead th {
        text-align: center;
        vertical-align: middle;
        font-size: 0.7rem;
        padding: 0.75rem 0.5rem;
        white-space: nowrap;
    }

    /* Đảm bảo các cột có độ rộng phù hợp */
    .table-responsive {
        overflow-x: auto;
    }

    /* Đảm bảo các cột có độ rộng tương đối phù hợp */
    .question-text-column {
        width: 18%;
        text-align: center;
    }

    .survey-name-column {
        width: 12%;
        text-align: center;
    }

    .order-column, .type-column, .required-column, .status-column, .risk-weight-column {
        width: 8%;
    }

    .options-column, .help-text-column {
        width: 15%;
        text-align: center;
    }

    .date-column {
        width: 10%;
    }

    .action-column {
        width: 12%;
    }
    
    /* Đảm bảo các ô có nội dung dài được căn lề trái */
    .table td.content-cell {
        text-align: left !important;
        vertical-align: middle;
        line-height: 1.3;
    }
    
    /* Định dạng cho cột ngày tháng */
    .formatted-date {
        position: relative;
    }

    .formatted-date::before {
        content: attr(data-date);
        display: block;
        font-weight: 500;
        color: #4a5568;
    }

    .formatted-date::after {
        content: attr(data-time);
        display: block;
        font-size: 0.75rem;
        color: #718096;
    }

    /* Ẩn nội dung gốc */
    .formatted-date {
        color: transparent;
        font-size: 0;
    }
</style>

<script src="~/js/signalr/dist/browser/signalr.js"></script>
<script>
    "use strict";

    var connection = new signalR.HubConnectionBuilder().withUrl("/DrugPreventionHub").build();

    connection.start().then(function () {
        console.log("Connected to the SignalR Hub");
    }).catch(function (err) {
        return console.error(err.toString());
    });

    connection.on("Receiver_DeleteSurveyQuestionsNamND", function (QuestionNamNDID) {
        $('#SurveyQuestionTBodyId').find(`tr[id='${QuestionNamNDID}']`).remove();
    });

    connection.on("Receiver_CreateSurveyQuestionsNamND", function (item){
        //alert('Receiver_CreateSurveyQuestionsNamND');
        console.log(item);
        
        var trSurveyQuestionsNamND = `
    <tr id="${item.questionNamNDID}">
        <td class="content-cell" style="text-align: left;">${item.questionText}</td>
        <td class="content-cell" style="text-align: left;">${item.surveyName}</td>
        <td>${item.questionOrder}</td>
        <td>${item.questionType}</td>
        <td>
            <span class="badge ${item.isRequired ? 'badge-required' : 'badge-optional'}">
                ${item.isRequired ? 'Required' : 'Optional'}
            </span>
        </td>
        <td>
            <span class="badge ${item.isActive ? 'badge-active' : 'badge-inactive'}">
                ${item.isActive ? 'Active' : 'Inactive'}
            </span>
        </td>
        <td class="content-cell" style="text-align: left;">${item.options}</td>
        <td>${item.riskWeight}</td>
        <td class="content-cell" style="text-align: left;">${item.helpText}</td>
        <td class="date-column formatted-date">${item.createdDate}</td>
        <td class="action-column">
            <div class="action-buttons">
                <a href="/SurveyQuestionsNamNDs/Edit?id=${item.questionNamNDID}" class="btn btn-edit">Edit</a>
                <a href="/SurveyQuestionsNamNDs/Details?id=${item.questionNamNDID}" class="btn btn-details">Details</a>
                <a href="/SurveyQuestionsNamNDs/Delete?id=${item.questionNamNDID}" class="btn btn-delete">Delete</a>
            </div>
        </td>
    </tr>
    `;
        alert(trSurveyQuestionsNamND);

        $('#SurveyQuestionTBodyId').append(trSurveyQuestionsNamND);
    });

    connection.on("Receiver_UpdateSurveyQuestionsNamND", function (item){
        //alert('Receiver_UpdateSurveyQuestionsNamND');
        console.log(item);
        
        var trSurveyQuestionsNamND = `
    <tr id="${item.questionNamNDID}">
        <td class="content-cell" style="text-align: left;">${item.questionText}</td>
        <td class="content-cell" style="text-align: left;">${item.surveyName}</td>
        <td>${item.questionOrder}</td>
        <td>${item.questionType}</td>
        <td>
            <span class="badge ${item.isRequired ? 'badge-required' : 'badge-optional'}">
                ${item.isRequired ? 'Required' : 'Optional'}
            </span>
        </td>
        <td>
            <span class="badge ${item.isActive ? 'badge-active' : 'badge-inactive'}">
                ${item.isActive ? 'Active' : 'Inactive'}
            </span>
        </td>
        <td class="content-cell" style="text-align: left;">${item.options}</td>
        <td>${item.riskWeight}</td>
        <td class="content-cell" style="text-align: left;">${item.helpText}</td>
        <td class="date-column formatted-date">${item.createdDate}</td>
        <td class="action-column">
            <div class="action-buttons">
                <a href="/SurveyQuestionsNamNDs/Edit?id=${item.questionNamNDID}" class="btn btn-edit">Edit</a>
                <a href="/SurveyQuestionsNamNDs/Details?id=${item.questionNamNDID}" class="btn btn-details">Details</a>
                <a href="/SurveyQuestionsNamNDs/Delete?id=${item.questionNamNDID}" class="btn btn-delete">Delete</a>
            </div>
        </td>
    </tr>
    `;

          alert(trSurveyQuestionsNamND);

          $(`#${item.questionNamNDID}`).replaceWith(trSurveyQuestionsNamND);
    });
</script>

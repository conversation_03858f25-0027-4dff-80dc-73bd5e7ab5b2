﻿@page
@model IndexModel
@{
    ViewData["Title"] = "Home";
}

<div class="container-fluid mt-4">
    <div class="row">
        <!-- Hero Section -->
        <div class="col-12 mb-4">
            <div class="card shadow-sm border-0 rounded-lg overflow-hidden">
                <div class="card-body p-0">
                    <div class="hero-banner bg-gradient-primary text-white p-5">
                        <div class="row align-items-center">
                            <div class="col-lg-7">
                                <h1 class="display-4 fw-bold mb-3">Drug Prevention Platform</h1>
                                <p class="lead mb-4">A comprehensive solution for drug prevention surveys, analysis, and intervention strategies.</p>
                                <div class="d-flex flex-wrap gap-2">
                                    <a href="/SurveyQuestionsNamNDs" class="btn btn-light btn-lg px-4 me-md-2">
                                        <i class="fas fa-clipboard-list me-2"></i>View Surveys
                                    </a>
                                    <a href="/SurveyQuestionsNamNDs/Create" class="btn btn-outline-light btn-lg px-4">
                                        <i class="fas fa-plus-circle me-2"></i>Create New
                                    </a>
                                </div>
                            </div>
                            <div class="col-lg-5 d-none d-lg-block">
                                <img src="https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80" 
                                     class="img-fluid rounded-3 hero-image" alt="Drug Prevention">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Features Section -->
    <div class="row mb-5">
        <div class="col-12 text-center mb-4">
            <h2 class="section-title">Key Features</h2>
            <p class="section-subtitle">Discover what our platform offers</p>
        </div>
        
        <div class="col-md-4 mb-4">
            <div class="card h-100 shadow-sm hover-card">
                <div class="card-body text-center p-4">
                    <div class="feature-icon-wrapper mb-3">
                        <i class="fas fa-poll feature-icon text-primary"></i>
                    </div>
                    <h3 class="h4 mb-3">Survey Management</h3>
                    <p class="card-text">Create, edit, and manage comprehensive surveys with various question types and scoring systems.</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-4 mb-4">
            <div class="card h-100 shadow-sm hover-card">
                <div class="card-body text-center p-4">
                    <div class="feature-icon-wrapper mb-3">
                        <i class="fas fa-chart-bar feature-icon text-success"></i>
                    </div>
                    <h3 class="h4 mb-3">Data Analysis</h3>
                    <p class="card-text">Analyze survey results with powerful tools to identify trends and risk factors.</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-4 mb-4">
            <div class="card h-100 shadow-sm hover-card">
                <div class="card-body text-center p-4">
                    <div class="feature-icon-wrapper mb-3">
                        <i class="fas fa-shield-alt feature-icon text-danger"></i>
                    </div>
                    <h3 class="h4 mb-3">Intervention Strategies</h3>
                    <p class="card-text">Develop targeted intervention strategies based on survey data and risk assessments.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- About Section -->
    <div class="row mb-5">
        <div class="col-lg-6 mb-4 mb-lg-0">
            <div class="card shadow-sm h-100">
                <div class="card-body p-4">
                    <h2 class="h3 mb-3">About Our Platform</h2>
                    <p>The Drug Prevention Platform is designed to help organizations create, manage, and analyze surveys related to drug prevention efforts. Our comprehensive solution enables:</p>
                    <ul class="feature-list">
                        <li><i class="fas fa-check-circle text-success me-2"></i>Customizable survey creation</li>
                        <li><i class="fas fa-check-circle text-success me-2"></i>Multi-language support</li>
                        <li><i class="fas fa-check-circle text-success me-2"></i>Risk assessment scoring</li>
                        <li><i class="fas fa-check-circle text-success me-2"></i>Real-time data updates via SignalR</li>
                        <li><i class="fas fa-check-circle text-success me-2"></i>Comprehensive reporting tools</li>
                    </ul>
                    <a href="building Web apps with ASP.NET Core" class="btn btn-primary mt-3">
                        <i class="fas fa-info-circle me-2"></i>Learn More
                    </a>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card shadow-sm h-100">
                <div class="card-body p-4">
                    <h2 class="h3 mb-3">Getting Started</h2>
                    <p>Follow these steps to begin using our platform:</p>
                    <div class="steps-container">
                        <div class="step-item">
                            <div class="step-number">1</div>
                            <div class="step-content">
                                <h4 class="h5">Create a Survey</h4>
                                <p>Design your survey with various question types and options.</p>
                            </div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">2</div>
                            <div class="step-content">
                                <h4 class="h5">Collect Responses</h4>
                                <p>Distribute your survey and gather responses from participants.</p>
                            </div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">3</div>
                            <div class="step-content">
                                <h4 class="h5">Analyze Results</h4>
                                <p>Review data and generate insights for intervention strategies.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    /* Hero Section */
    .bg-gradient-primary {
        background: linear-gradient(135deg, #3a47d5 0%, #00d2ff 100%);
    }
    
    .hero-banner {
        position: relative;
        overflow: hidden;
        border-radius: 0.5rem;
    }
    
    .hero-image {
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        transition: transform 0.3s ease;
    }
    
    /* Section Styling */
    .section-title {
        color: #333;
        font-weight: 700;
        margin-bottom: 0.5rem;
        position: relative;
        display: inline-block;
    }
    
    .section-title:after {
        content: '';
        display: block;
        width: 50px;
        height: 3px;
        background: #3a47d5;
        margin: 0.5rem auto 0;
    }
    
    .section-subtitle {
        color: #6c757d;
        font-size: 1.1rem;
        margin-bottom: 2rem;
    }
    
    /* Feature Cards */
    .feature-icon-wrapper {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background-color: rgba(58, 71, 213, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
    }
    
    .feature-icon {
        font-size: 2rem;
    }
    
    .hover-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    
    .hover-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
    }
    
    /* Feature List */
    .feature-list {
        list-style: none;
        padding-left: 0;
    }
    
    .feature-list li {
        margin-bottom: 0.75rem;
        display: flex;
        align-items: center;
    }
    
    /* Steps */
    .steps-container {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
    }
    
    .step-item {
        display: flex;
        gap: 1rem;
    }
    
    .step-number {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #3a47d5;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        flex-shrink: 0;
    }
    
    .step-content {
        flex-grow: 1;
    }
    
    .step-content h4 {
        margin-bottom: 0.25rem;
        color: #333;
    }
    
    .step-content p {
        margin-bottom: 0;
        color: #6c757d;
    }
    
</style>

<!-- Add Font Awesome if not already included in _Layout.cshtml -->
</div>

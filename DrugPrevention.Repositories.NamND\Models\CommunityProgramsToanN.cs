﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DrugPrevention.Repositories.NamND.Models;

public partial class CommunityProgramsToanN
{
    public int ProgramToanNSID { get; set; }

    public string ProgramName { get; set; }

    public DateTime StartDate { get; set; }

    public DateTime EndDate { get; set; }

    public string Location { get; set; }

    public int? MaxParticipants { get; set; }

    public int? CurrentParticipants { get; set; }

    public string Status { get; set; }

    public int? OrganizerId { get; set; }

    public bool? IsActive { get; set; }

    public virtual UsersTuyenTM Organizer { get; set; }

    public virtual ICollection<ProgramParticipantsToanN> ProgramParticipantsToanNs { get; set; } = new List<ProgramParticipantsToanN>();
}
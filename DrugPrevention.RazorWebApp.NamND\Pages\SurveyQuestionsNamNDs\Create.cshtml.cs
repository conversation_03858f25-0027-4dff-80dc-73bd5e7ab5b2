﻿using DrugPrevention.Repositories.NamND.Models;
using DrugPrevention.Services.NamND;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace DrugPrevention.RazorWebApp.NamND.Pages.SurveyQuestionsNamNDs
{
    [Authorize(Roles = "1,2")]
    public class CreateModel : PageModel
    {
        //private readonly DrugPrevention.Repositories.NamND.Models.SU25_PRN222_SE1709_G2_DrugPreventionSystemContext _context;

        private readonly ISurveyQuestionsNamNDService _surveyQuestionsNamNDService;
        public readonly SurveysNamNDService _surveysNamNDService;
        public CreateModel(ISurveyQuestionsNamNDService surveyQuestionsNamNDService, SurveysNamNDService surveysNamNDService)
        {
            _surveyQuestionsNamNDService = surveyQuestionsNamNDService;
            _surveysNamNDService = surveysNamNDService;
        }

        public async Task<IActionResult> OnGet()
        {
            var surveys = await _surveysNamNDService.GetAllAsync();
            ViewData["SurveyNamNDID"] = new SelectList(surveys, "SurveyNamNDID", "SurveyName");

            // Initialize the model with default values (constructor handles defaults)
            SurveyQuestionsNamND = new SurveyQuestionsNamND();

            return Page();
        }

        [BindProperty]
        public SurveyQuestionsNamND SurveyQuestionsNamND { get; set; } = default!;

        // For more information, see https://aka.ms/RazorPagesCRUD.
        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                return Page();
            }

            var result = await _surveyQuestionsNamNDService.CreateAsync(SurveyQuestionsNamND);

            return RedirectToPage("./Index");
        }
    }
}

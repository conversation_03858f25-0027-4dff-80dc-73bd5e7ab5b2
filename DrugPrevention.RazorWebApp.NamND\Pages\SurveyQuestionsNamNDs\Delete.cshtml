﻿@page
@model DrugPrevention.RazorWebApp.NamND.Pages.SurveyQuestionsNamNDs.DeleteModel

@{
    ViewData["Title"] = "Delete Survey Question";
}

<div class="container-fluid mt-4">
    <div class="card shadow-sm mb-4">
        <div class="card-header bg-gradient-danger text-white py-3">
            <div class="d-flex justify-content-center align-items-center">
                <h1 class="h3 mb-0 font-weight-bold text-white">
                    <i class="fas fa-exclamation-triangle me-2"></i>Delete Survey Question
                </h1>
            </div>
        </div>
        <div class="card-body p-4">
            <div class="alert alert-warning mb-4" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Warning:</strong> Are you sure you want to delete this survey question? This action cannot be undone.
            </div>

            <div class="details-list">
        <!-- Group 1: Identification & Context -->
        <div class="row">
            <dt class="col-sm-3">
                @Html.DisplayNameFor(model => model.SurveyQuestionsNamND.QuestionText)
            </dt>
            <dd class="col-sm-9">
                @Html.DisplayFor(model => model.SurveyQuestionsNamND.QuestionText)
            </dd>
        </div>

        <div class="row">
            <dt class="col-sm-3">
                @Html.DisplayNameFor(model => model.SurveyQuestionsNamND.SurveyNamND)
            </dt>
            <dd class="col-sm-9">
                @Html.DisplayFor(model => model.SurveyQuestionsNamND.SurveyNamND.SurveyName)
            </dd>
        </div>

        <div class="row">
            <dt class="col-sm-3">
                @Html.DisplayNameFor(model => model.SurveyQuestionsNamND.QuestionOrder)
            </dt>
            <dd class="col-sm-9">
                @Html.DisplayFor(model => model.SurveyQuestionsNamND.QuestionOrder)
            </dd>
        </div>

        <!-- Group 2: Basic Configuration -->
        <div class="row">
            <dt class="col-sm-3">
                @Html.DisplayNameFor(model => model.SurveyQuestionsNamND.QuestionType)
            </dt>
            <dd class="col-sm-9">
                @Html.DisplayFor(model => model.SurveyQuestionsNamND.QuestionType)
            </dd>
        </div>

        <div class="row">
            <dt class="col-sm-3">
                @Html.DisplayNameFor(model => model.SurveyQuestionsNamND.IsRequired)
            </dt>
            <dd class="col-sm-9">
                <span class="required-badge @(Model.SurveyQuestionsNamND.IsRequired ? "required" : "optional")">
                    @(Model.SurveyQuestionsNamND.IsRequired ? "Required" : "Optional")
                </span>
            </dd>
        </div>

        <div class="row">
            <dt class="col-sm-3">
                @Html.DisplayNameFor(model => model.SurveyQuestionsNamND.IsActive)
            </dt>
            <dd class="col-sm-9">
                <span class="status-badge @(Model.SurveyQuestionsNamND.IsActive ? "active" : "inactive")">
                    @(Model.SurveyQuestionsNamND.IsActive ? "Active" : "Inactive")
                </span>
            </dd>
        </div>

        <!-- Group 3: Detailed Content -->
        <div class="row">
            <dt class="col-sm-3">
                @Html.DisplayNameFor(model => model.SurveyQuestionsNamND.Options)
            </dt>
            <dd class="col-sm-9">
                @Html.DisplayFor(model => model.SurveyQuestionsNamND.Options)
            </dd>
        </div>

        <div class="row">
            <dt class="col-sm-3">
                @Html.DisplayNameFor(model => model.SurveyQuestionsNamND.RiskWeight)
            </dt>
            <dd class="col-sm-9">
                @Html.DisplayFor(model => model.SurveyQuestionsNamND.RiskWeight)
            </dd>
        </div>

        <div class="row">
            <dt class="col-sm-3">
                @Html.DisplayNameFor(model => model.SurveyQuestionsNamND.HelpText)
            </dt>
            <dd class="col-sm-9">
                @Html.DisplayFor(model => model.SurveyQuestionsNamND.HelpText)
            </dd>
        </div>

        <!-- Group 4: Metadata -->
        <div class="row">
            <dt class="col-sm-3">
                @Html.DisplayNameFor(model => model.SurveyQuestionsNamND.CreatedDate)
            </dt>
            <dd class="col-sm-9">
                @Html.DisplayFor(model => model.SurveyQuestionsNamND.CreatedDate)
            </dd>
        </div>

        <!-- Less Important Fields - Commented for cleaner view -->
        @*
        <div class="row">
            <dt class="col-sm-3">
                @Html.DisplayNameFor(model => model.SurveyQuestionsNamND.Section)
            </dt>
            <dd class="col-sm-9">
                @Html.DisplayFor(model => model.SurveyQuestionsNamND.Section)
            </dd>
        </div>

        <div class="row">
            <dt class="col-sm-3">
                @Html.DisplayNameFor(model => model.SurveyQuestionsNamND.Tag)
            </dt>
            <dd class="col-sm-9">
                @Html.DisplayFor(model => model.SurveyQuestionsNamND.Tag)
            </dd>
        </div>

        <div class="row">
            <dt class="col-sm-3">
                @Html.DisplayNameFor(model => model.SurveyQuestionsNamND.Language)
            </dt>
            <dd class="col-sm-9">
                @Html.DisplayFor(model => model.SurveyQuestionsNamND.Language)
            </dd>
        </div>

        <div class="row">
            <dt class="col-sm-3">
                @Html.DisplayNameFor(model => model.SurveyQuestionsNamND.DependsOnQuestionID)
            </dt>
            <dd class="col-sm-9">
                @Html.DisplayFor(model => model.SurveyQuestionsNamND.DependsOnQuestionID)
            </dd>
        </div>

        <div class="row">
            <dt class="col-sm-3">
                @Html.DisplayNameFor(model => model.SurveyQuestionsNamND.DependsOnAnswer)
            </dt>
            <dd class="col-sm-9">
                @Html.DisplayFor(model => model.SurveyQuestionsNamND.DependsOnAnswer)
            </dd>
        </div>

        <div class="row">
            <dt class="col-sm-3">
                @Html.DisplayNameFor(model => model.SurveyQuestionsNamND.MinScore)
            </dt>
            <dd class="col-sm-9">
                @Html.DisplayFor(model => model.SurveyQuestionsNamND.MinScore)
            </dd>
        </div>

        <div class="row">
            <dt class="col-sm-3">
                @Html.DisplayNameFor(model => model.SurveyQuestionsNamND.MaxScore)
            </dt>
            <dd class="col-sm-9">
                @Html.DisplayFor(model => model.SurveyQuestionsNamND.MaxScore)
            </dd>
        </div>

        <div class="row">
            <dt class="col-sm-3">
                @Html.DisplayNameFor(model => model.SurveyQuestionsNamND.DisplayStyle)
            </dt>
            <dd class="col-sm-9">
                @Html.DisplayFor(model => model.SurveyQuestionsNamND.DisplayStyle)
            </dd>
        </div>

        <div class="row">
            <dt class="col-sm-3">
                @Html.DisplayNameFor(model => model.SurveyQuestionsNamND.ImageURL)
            </dt>
            <dd class="col-sm-9">
                @Html.DisplayFor(model => model.SurveyQuestionsNamND.ImageURL)
            </dd>
        </div>
        *@
            </div>
        </div>
        <div class="card-footer bg-light py-3">
            <form method="post">
                <input type="hidden" asp-for="SurveyQuestionsNamND.QuestionNamNDID" />
                <div class="action-buttons-footer">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i>Delete
                    </button>
                    <button type="button" id="btnHubDelete" class="btn btn-primary">
                        <i class="fas fa-broadcast-tower me-1"></i>Delete by SignalRHub
                    </button>
                    <a asp-page="./Index" class="btn btn-secondary">
                        <i class="fas fa-list me-1"></i>Back to List
                    </a>
                </div>
                <div class="mt-2 text-center">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        SignalR: Send ID → SignalRHub → send ID to all Clients → Delete
                    </small>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
    /* Container styling */
    .container-fluid {
        max-width: 1200px;
        margin: 0 auto;
    }

    /* Card styling */
    .card {
        border: none;
        border-radius: 0.5rem;
        overflow: hidden;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
    }

    .card-header {
        padding: 1rem 1.35rem;
        border-bottom: none;
    }

    .card-body {
        padding: 2rem 1.5rem;
    }

    .card-footer {
        border-top: 1px solid #e3e6f0;
        padding: 1rem 1.35rem;
    }

    /* Gradient backgrounds */
    .bg-gradient-danger {
        background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    }

    /* Alert styling */
    .alert-warning {
        border-left: 4px solid #f39c12;
        background-color: #fef9e7;
        border-color: #f39c12;
        color: #8a6d3b;
        padding: 1rem;
        border-radius: 0.35rem;
        margin-bottom: 1.5rem;
    }

    /* Details list styling */
    .details-list {
        margin-bottom: 0;
    }

    .details-list .row {
        margin-bottom: 1rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #f0f0f0;
    }

    .details-list .row:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
    }

    .details-list dt {
        font-weight: 600;
        color: #2c3e50;
        font-size: 0.9rem;
    }

    .details-list dd {
        font-size: 1rem;
        color: #4a5568;
    }

    /* Button styling */
    .btn {
        padding: 0.6rem 1.5rem;
        font-size: 0.95rem;
        font-weight: 600;
        border-radius: 0.35rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border: 1px solid;
        cursor: pointer;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        letter-spacing: 0.3px;
    }

    .btn i {
        margin-right: 0.5rem;
        font-size: 1rem;
    }

    .btn-danger {
        background-color: #e74c3c;
        border-color: #e74c3c;
        color: white;
    }

    .btn-danger:hover {
        background-color: #c0392b;
        border-color: #c0392b;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(231, 76, 60, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-primary {
        background-color: #3498db;
        border-color: #3498db;
        color: white;
    }

    .btn-primary:hover {
        background-color: #2980b9;
        border-color: #2980b9;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(52, 152, 219, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-secondary {
        background-color: #95a5a6;
        border-color: #95a5a6;
        color: white;
    }

    .btn-secondary:hover {
        background-color: #7f8c8d;
        border-color: #7f8c8d;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(149, 165, 166, 0.3);
        color: white;
        text-decoration: none;
    }

    /* Status badges */
    .status-badge, .required-badge {
        display: inline-block;
        padding: 0.35em 0.65em;
        font-size: 0.75em;
        font-weight: 700;
        line-height: 1;
        text-align: center;
        white-space: nowrap;
        vertical-align: baseline;
        border-radius: 0.25rem;
    }

    .active {
        background-color: #2ecc71;
        color: white;
    }

    .inactive {
        background-color: #e74c3c;
        color: white;
    }

    .required {
        background-color: #3498db;
        color: white;
    }

    .optional {
        background-color: #95a5a6;
        color: white;
    }

    /* Action buttons footer */
    .action-buttons-footer {
        display: flex;
        gap: 1rem;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
    }

    /* Small text styling */
    .text-muted {
        font-size: 0.875rem;
        color: #7f8c8d !important;
    }

</style>

<script src="~/js/signalr/dist/browser/signalr.js"></script>
<script>
    "use strict";

    //// Get "zPaymentHub" from app.MapHub<zPaymentHub>("/zPaymentHub") at Program.cs;
    var connection = new signalR.HubConnectionBuilder().withUrl("/DrugPreventionHub").build();

    connection.start().then(function () {
        console.log("Connected to the SignalR Hub");
    }).catch(function (err) {
        return console.error(err.toString());
    });

    document.getElementById("btnHubDelete").addEventListener("click", function (event) {

        //alert("btnHubDelete");

        var dataObj = document.getElementById("SurveyQuestionsNamND_QuestionNamNDID").value;
        console.log(dataObj);

        //// Calls method of SignalRHub server with primary key param (zPaymentHub.cs)
        connection.invoke("HubDelete_SurveyQuestion", dataObj).catch(function (err) {
            return console.error(err.toString());
        });
        event.preventDefault();
    });

</script>

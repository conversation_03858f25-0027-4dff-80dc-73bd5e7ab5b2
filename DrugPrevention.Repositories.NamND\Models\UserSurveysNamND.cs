﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DrugPrevention.Repositories.NamND.Models;

public partial class UserSurveysNamND
{
    public int UserSurveyNamNDID { get; set; }

    public int UserID { get; set; }

    public int SurveyID { get; set; }

    public DateTime SubmissionDate { get; set; }

    public int? RiskScore { get; set; }

    public string Recommendations { get; set; }

    public int? CompletionTime { get; set; }

    public bool? IsAnonymous { get; set; }

    public string ResultSummary { get; set; }

    public string Feedback { get; set; }

    public string Status { get; set; }

    public int? AttemptNumber { get; set; }

    public string DeviceInfo { get; set; }

    public string Location { get; set; }

    public string IPAddress { get; set; }

    public DateTime? StartTime { get; set; }

    public DateTime? EndTime { get; set; }

    public string Language { get; set; }

    public bool? IsCompleted { get; set; }

    public virtual SurveysNamND Survey { get; set; }

    public virtual UsersTuyenTM User { get; set; }
}